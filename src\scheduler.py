"""
Scheduling system for posting Instagram reels at optimal times
"""
import json
import random
import schedule
import time
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import threading

import pytz

from src.config import Config
from src.utils.logger import logger
from src.message_processor import MessageProcessor, ProcessedMessage
from src.instagram_client import InstagramClient

class PostScheduler:
    """Handles scheduling and posting of Instagram reels"""
    
    def __init__(self):
        self.message_processor = MessageProcessor()
        self.instagram_client = InstagramClient()
        self.schedule_file = Config.BASE_DIR / "post_schedule.json"

        # Check if schedule reset is requested
        if Config.RESET_SCHEDULE:
            self._reset_schedule()

        self.scheduled_posts = self._load_schedule()
        self.is_running = False
        self.scheduler_thread = None

        # Tehran timezone for scheduling
        self.tehran_tz = pytz.timezone('Asia/Tehran')

        # Entertainment profile posting schedule (9 AM to 12 midnight Tehran time)
        self.posting_start_hour = 9
        self.posting_end_hour = 24  # 24 = midnight (00:00 next day)
        self.max_daily_posts = 8  # 6-8 posts during the 15-hour window
    
    def _load_schedule(self) -> List[Dict[str, Any]]:
        """Load scheduled posts from file"""
        if self.schedule_file.exists():
            try:
                with open(self.schedule_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('scheduled_posts', [])
            except Exception as e:
                logger.warning(f"Could not load schedule: {e}")
        return []
    
    def _save_schedule(self):
        """Save scheduled posts to file"""
        try:
            data = {
                'scheduled_posts': self.scheduled_posts,
                'last_updated': datetime.now(self.tehran_tz).isoformat()
            }
            with open(self.schedule_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save schedule: {e}")

    def _reset_schedule(self):
        """Reset schedule and clean up associated video files"""
        try:
            logger.info("🔄 RESET_SCHEDULE=true detected - Clearing all scheduled posts and video files...")

            # Load existing schedule to get video paths
            if self.schedule_file.exists():
                try:
                    with open(self.schedule_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        scheduled_posts = data.get('scheduled_posts', [])

                    # Delete associated video files using robust cleanup
                    from .utils.file_utils import cleanup_multiple_files
                    video_paths = []
                    for entry in scheduled_posts:
                        video_path = Path(entry.get('video_path', ''))
                        if video_path.exists():
                            video_paths.append(video_path)

                    if video_paths:
                        deleted_count = cleanup_multiple_files(video_paths, delay_between_files=0.2)
                        logger.info(f"🗑️  Deleted {deleted_count}/{len(video_paths)} video files")
                    else:
                        logger.info("🗑️  No video files to delete")

                except Exception as e:
                    logger.warning(f"Could not load existing schedule for cleanup: {e}")

            # Delete schedule file
            if self.schedule_file.exists():
                self.schedule_file.unlink()
                logger.info("🗑️  Deleted schedule file")

            # FULL RESET: Reset both video generation and Instagram posted status
            reset_count = 0
            for message in self.message_processor.processing_queue:
                if message.video_generated or message.instagram_posted:
                    message.video_generated = False
                    message.instagram_posted = False
                    message.output_video_path = None
                    reset_count += 1
                    logger.info(f"🔄 Full reset for message {message.telegram_message.message_id} (video_generated=False, instagram_posted=False)")

            logger.info(f"🔄 Reset {reset_count} messages to unprocessed state")

            # Save updated processing queue
            self.message_processor._save_processing_queue()

            logger.info("✅ Schedule reset completed successfully")

        except Exception as e:
            logger.error(f"Error resetting schedule: {e}")

    def _get_optimal_posting_times(self) -> List[int]:
        """
        Get optimal posting times for entertainment profile
        9 AM to 12 midnight Tehran time for content creator schedule
        """
        # Entertainment profile posting hours (9 AM to 12 midnight Tehran time)
        # Include all hours in posting window for better distribution
        return list(range(self.posting_start_hour, self.posting_end_hour))  # 9 AM to 11 PM (all hours)
    
    def _generate_random_post_time(self) -> datetime:
        """Generate a random posting time within 9 AM to 12 PM Tehran time"""
        # Get current time in Tehran timezone
        now_tehran = datetime.now(self.tehran_tz)

        # Check if we're currently in posting window (9 AM to 12 PM)
        current_hour = now_tehran.hour

        # Get optimal hours (9, 10, 11)
        optimal_hours = self._get_optimal_posting_times()

        # Determine target date
        if current_hour < self.posting_start_hour:
            # Before 9 AM today - schedule for today
            target_date = now_tehran.date()
        elif current_hour >= self.posting_end_hour:
            # After 12 PM today - schedule for tomorrow
            target_date = now_tehran.date() + timedelta(days=1)
        else:
            # Currently in posting window - check if we can still post today
            remaining_hours = [h for h in optimal_hours if h > current_hour]
            if remaining_hours and self._can_post_more_today():
                target_date = now_tehran.date()
            else:
                target_date = now_tehran.date() + timedelta(days=1)

        # Choose a random hour from optimal hours
        target_hour = random.choice(optimal_hours)

        # Add randomness to minutes (0-59)
        target_minute = random.randint(0, 59)

        # Create target datetime in Tehran timezone
        target_time = self.tehran_tz.localize(
            datetime.combine(target_date, datetime.min.time().replace(
                hour=target_hour, minute=target_minute
            ))
        )

        # Convert to UTC for storage
        return target_time.astimezone(pytz.UTC).replace(tzinfo=None)

    def _can_post_more_today(self) -> bool:
        """Check if we can post more content today based on daily limits"""
        today_tehran = datetime.now(self.tehran_tz).date()

        # Count posts scheduled/posted for today
        posts_today = 0
        for entry in self.scheduled_posts:
            try:
                # Convert scheduled time to Tehran timezone for comparison
                scheduled_utc = datetime.fromisoformat(entry['scheduled_time'])
                scheduled_tehran = pytz.UTC.localize(scheduled_utc).astimezone(self.tehran_tz)

                if scheduled_tehran.date() == today_tehran:
                    posts_today += 1
            except Exception:
                continue

        return posts_today < self.max_daily_posts

    def _get_next_available_slot(self) -> datetime:
        """Get the next available posting slot for optimal distribution"""
        today_tehran = datetime.now(self.tehran_tz).date()

        # Get all scheduled times for today
        today_slots = []
        for entry in self.scheduled_posts:
            try:
                scheduled_utc = datetime.fromisoformat(entry['scheduled_time'])
                scheduled_tehran = pytz.UTC.localize(scheduled_utc).astimezone(self.tehran_tz)

                if scheduled_tehran.date() == today_tehran:
                    today_slots.append(scheduled_tehran.hour)
            except Exception:
                continue

        # Find available slots for today
        now_tehran = datetime.now(self.tehran_tz)
        current_hour = now_tehran.hour

        # Get available hours for today (any hour in posting window that's not taken and hasn't passed)
        available_hours = []
        for h in self._get_optimal_posting_times():
            if h not in today_slots:
                # If we're currently in posting window, only allow current hour and future hours
                if self.posting_start_hour <= current_hour < self.posting_end_hour:
                    if h >= current_hour:
                        available_hours.append(h)
                # If we're outside posting window (early morning), allow all posting hours for today
                else:
                    available_hours.append(h)

        if available_hours:
            # Use today if slots available
            target_hour = min(available_hours)  # Use earliest available slot

            # If it's the current hour, schedule for a few minutes from now
            if target_hour == current_hour:
                target_minute = min(59, now_tehran.minute + random.randint(5, 15))  # 5-15 minutes from now
            else:
                target_minute = random.randint(0, 59)

            target_time = self.tehran_tz.localize(
                datetime.combine(today_tehran, datetime.min.time().replace(
                    hour=target_hour, minute=target_minute
                ))
            )

            # Double-check if the time has passed (shouldn't happen with new logic)
            if target_time <= now_tehran:
                # Schedule for next available hour today or tomorrow
                next_hours = [h for h in self._get_optimal_posting_times()
                             if h > current_hour and h not in today_slots and h < self.posting_end_hour]
                if next_hours:
                    target_hour = min(next_hours)
                    target_time = self.tehran_tz.localize(
                        datetime.combine(today_tehran, datetime.min.time().replace(
                            hour=target_hour, minute=random.randint(0, 59)
                        ))
                    )
                else:
                    # Schedule for tomorrow
                    tomorrow = today_tehran + timedelta(days=1)
                    target_hour = random.choice(self._get_optimal_posting_times())
                    target_time = self.tehran_tz.localize(
                        datetime.combine(tomorrow, datetime.min.time().replace(
                            hour=target_hour, minute=random.randint(0, 59)
                        ))
                    )
        else:
            # Schedule for tomorrow
            tomorrow = today_tehran + timedelta(days=1)
            target_hour = random.choice(self._get_optimal_posting_times())
            target_minute = random.randint(0, 59)

            target_time = self.tehran_tz.localize(
                datetime.combine(tomorrow, datetime.min.time().replace(
                    hour=target_hour, minute=target_minute
                ))
            )

        return target_time.astimezone(pytz.UTC).replace(tzinfo=None)

    def schedule_post(self, processed_message: ProcessedMessage) -> bool:
        """
        Schedule a processed message for posting
        
        Args:
            processed_message: The message to schedule
            
        Returns:
            True if scheduled successfully, False otherwise
        """
        try:
            if not processed_message.video_generated or not processed_message.output_video_path:
                logger.error("Cannot schedule post: video not generated")
                return False
            
            # Generate optimal posting time with better distribution
            post_time = self._get_next_available_slot()
            
            # Create schedule entry
            schedule_entry = {
                'id': f"post_{processed_message.telegram_message.message_id}_{int(time.time())}",
                'message_id': processed_message.telegram_message.message_id,
                'video_path': str(processed_message.output_video_path),
                'caption': processed_message.text_content,
                'scheduled_time': post_time.isoformat(),
                'status': 'scheduled',
                'created_at': datetime.now(self.tehran_tz).isoformat(),
                'attempts': 0,
                'max_attempts': 3
            }
            
            self.scheduled_posts.append(schedule_entry)
            self._save_schedule()

            # Mark message as scheduled to prevent re-scheduling
            self.message_processor.mark_scheduled_for_posting(processed_message)

            logger.info(f"Scheduled post for {post_time.strftime('%Y-%m-%d %H:%M:%S')}: {processed_message.telegram_message.message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling post: {e}")
            return False
    
    def schedule_ready_posts(self) -> int:
        """
        Schedule posts that are ready for posting (with safety limit)

        Returns:
            Number of posts scheduled
        """
        # Don't schedule posts in instant posting mode
        if Config.INSTANT_POSTING_MODE:
            logger.debug("Skipping post scheduling - instant posting mode enabled")
            return 0

        try:
            ready_messages = self.message_processor.get_ready_for_posting()
            max_to_schedule = Config.MAX_POSTS_TO_SCHEDULE_AT_ONCE

            # Limit the number of posts to schedule at once
            if len(ready_messages) > max_to_schedule:
                logger.warning(f"Found {len(ready_messages)} ready posts, limiting to {max_to_schedule} per cycle")
                ready_messages = ready_messages[:max_to_schedule]

            scheduled_count = 0

            for message in ready_messages:
                if self.schedule_post(message):
                    scheduled_count += 1

            logger.info(f"Scheduled {scheduled_count} posts")
            return scheduled_count

        except Exception as e:
            logger.error(f"Error scheduling ready posts: {e}")
            return 0
    
    def _execute_scheduled_post(self, schedule_entry: Dict[str, Any]) -> bool:
        """Execute a scheduled post"""
        try:
            video_path = Path(schedule_entry['video_path'])
            caption = schedule_entry['caption']
            
            logger.info(f"Executing scheduled post: {schedule_entry['id']}")

            success = False
            media_id = None

            # Post to Instagram (if enabled and not in test mode)
            if Config.TEST_MODE:
                # Test mode: simulate posting without actually posting to Instagram
                logger.info(f"🧪 TEST MODE: Simulating Instagram post")
                logger.info(f"🧪 TEST MODE: Video: {video_path}")
                logger.info(f"🧪 TEST MODE: Caption: {caption[:100]}{'...' if len(caption) > 100 else ''}")

                # Simulate success
                success = True
                media_id = f"TEST_MEDIA_{schedule_entry['message_id']}_{int(time.time())}"

                # Send test notification to admin
                try:
                    now_tehran = datetime.now(self.tehran_tz)
                    test_message = (
                        f"🧪 <b>TEST MODE: Instagram Post Simulated!</b>\n\n"
                        f"📱 <b>Test Media ID:</b> {media_id}\n"
                        f"🕐 <b>Simulated at:</b> {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                        f"📝 <b>Caption:</b> {caption[:150]}{'...' if len(caption) > 150 else ''}\n\n"
                        f"✅ This would have been posted to Instagram in real mode."
                    )
                    self._send_notification_sync(test_message)
                except Exception as e:
                    logger.error(f"Error sending test notification: {e}")

            elif Config.ENABLE_INSTAGRAM_POSTING:
                # Real mode: actually post to Instagram
                media_id = self.instagram_client.post_reel(video_path, caption)
                if media_id:
                    logger.info(f"Successfully posted to Instagram: {media_id}")
                    success = True

                    # Send success notification to admin
                    try:
                        now_tehran = datetime.now(self.tehran_tz)
                        success_message = (
                            f"✅ <b>Instagram Post Successful!</b>\n\n"
                            f"📱 <b>Media ID:</b> {media_id}\n"
                            f"🕐 <b>Posted at:</b> {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                            f"📝 <b>Caption:</b> {caption[:150]}{'...' if len(caption) > 150 else ''}\n\n"
                            f"🔗 @linkychannell"
                        )
                        self._send_notification_sync(success_message)
                    except Exception as e:
                        logger.error(f"Error sending success notification: {e}")

                else:
                    logger.warning("Instagram posting failed")
                    success = False

                    # Send error notification to admin
                    try:
                        now_tehran = datetime.now(self.tehran_tz)
                        error_message = (
                            f"❌ <b>Instagram Posting Failed!</b>\n\n"
                            f"🕐 <b>Failed at:</b> {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                            f"📝 <b>Caption:</b> {caption[:150]}{'...' if len(caption) > 150 else ''}\n"
                            f"🔄 <b>Attempt:</b> {schedule_entry['attempts'] + 1}/{schedule_entry['max_attempts']}\n\n"
                            f"Will retry if attempts remaining."
                        )
                        self._send_notification_sync(error_message)
                    except Exception as e:
                        logger.error(f"Error sending error notification: {e}")
            else:
                logger.info("Instagram posting disabled, skipping")
                success = False  # Don't mark as posted if Instagram posting is disabled

            # Send video to Telegram (if enabled)
            if Config.ENABLE_TELEGRAM_VIDEO_SENDING:
                try:
                    import threading
                    import concurrent.futures
                    from .telegram_client import TelegramClient

                    def send_telegram_video():
                        """Send video in separate thread to avoid event loop conflicts"""
                        try:
                            import asyncio
                            # Create new event loop for this thread
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)

                            try:
                                telegram_client = TelegramClient()
                                telegram_caption = f"📹 <b>New Video Generated</b>\n\n{caption}\n\n🔗 @linkychannell"

                                result = loop.run_until_complete(
                                    telegram_client.send_video(video_path, telegram_caption)
                                )
                                return result
                            finally:
                                loop.close()
                        except Exception as e:
                            logger.error(f"Error in telegram video thread: {e}")
                            return False

                    # Run in separate thread to avoid event loop conflicts
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(send_telegram_video)
                        try:
                            telegram_success = future.result(timeout=60)  # 60 second timeout
                            if telegram_success:
                                logger.info("Successfully sent video to Telegram")
                            else:
                                logger.warning("Failed to send video to Telegram")
                        except concurrent.futures.TimeoutError:
                            logger.warning("Telegram video sending timed out")

                except Exception as e:
                    logger.error(f"Error sending video to Telegram: {e}")

            if success:
                # Update schedule entry
                schedule_entry['status'] = 'posted'
                schedule_entry['posted_at'] = datetime.now(self.tehran_tz).isoformat()
                if media_id:
                    schedule_entry['media_id'] = media_id

                # Mark message as posted
                message_id = schedule_entry['message_id']
                for message in self.message_processor.processing_queue:
                    if message.telegram_message.message_id == message_id:
                        self.message_processor.mark_instagram_posted(message)
                        break

                # Clean up video file after successful posting (with robust retry logic)
                from .utils.file_utils import cleanup_video_file_threaded
                cleanup_video_file_threaded(video_path, delay_before_cleanup=5.0)

                logger.info(f"Successfully executed scheduled post: {schedule_entry['id']}")
                return True
            else:
                # Update attempt count
                schedule_entry['attempts'] += 1
                schedule_entry['last_attempt'] = datetime.now(self.tehran_tz).isoformat()

                if schedule_entry['attempts'] >= schedule_entry['max_attempts']:
                    schedule_entry['status'] = 'failed'
                    logger.error(f"Post failed after {schedule_entry['max_attempts']} attempts: {schedule_entry['id']}")

                    # Send final failure notification to admin
                    try:
                        now_tehran = datetime.now(self.tehran_tz)
                        final_error_message = (
                            f"🚨 <b>Instagram Post PERMANENTLY FAILED!</b>\n\n"
                            f"📝 <b>Caption:</b> {caption[:150]}{'...' if len(caption) > 150 else ''}\n"
                            f"🔄 <b>Failed after:</b> {schedule_entry['max_attempts']} attempts\n"
                            f"🕐 <b>Final attempt:</b> {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                            f"📋 <b>Post ID:</b> {schedule_entry['id']}\n\n"
                            f"⚠️ Manual intervention may be required."
                        )
                        self._send_notification_sync(final_error_message)
                    except Exception as e:
                        logger.error(f"Error sending final failure notification: {e}")

                else:
                    # Reschedule for next posting window
                    new_time = self._generate_random_post_time()
                    schedule_entry['scheduled_time'] = new_time.isoformat()
                    logger.warning(f"Post failed, rescheduled for {new_time}: {schedule_entry['id']}")

                    # Send reschedule notification to admin
                    try:
                        reschedule_message = (
                            f"🔄 <b>Instagram Post Rescheduled</b>\n\n"
                            f"📝 <b>Caption:</b> {caption[:100]}{'...' if len(caption) > 100 else ''}\n"
                            f"🕐 <b>New time:</b> {new_time.astimezone(self.tehran_tz).strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                            f"🔄 <b>Attempt:</b> {schedule_entry['attempts']}/{schedule_entry['max_attempts']}"
                        )
                        self._send_notification_sync(reschedule_message)
                    except Exception as e:
                        logger.error(f"Error sending reschedule notification: {e}")
                
                return False
                
        except Exception as e:
            logger.error(f"Error executing scheduled post: {e}")
            schedule_entry['attempts'] += 1
            schedule_entry['last_error'] = str(e)
            schedule_entry['last_attempt'] = datetime.now(self.tehran_tz).isoformat()

            if schedule_entry['attempts'] >= schedule_entry['max_attempts']:
                schedule_entry['status'] = 'failed'

            return False

    def _cleanup_video_file(self, video_path: Path):
        """Clean up video file after successful posting (legacy method - use robust cleanup instead)"""
        from .utils.file_utils import cleanup_video_file_robust
        cleanup_video_file_robust(video_path, delay_before_cleanup=0.0)

    async def _send_telegram_notification(self, message: str, parse_mode: str = "HTML"):
        """Send notification to admin Telegram chat"""
        try:
            from .telegram_client import TelegramClient

            telegram_client = TelegramClient()
            await telegram_client.initialize()

            # Send to admin chat
            success = await telegram_client.send_message(
                Config.TELEGRAM_PERSONAL_CHAT_ID,
                message,
                parse_mode=parse_mode
            )

            if success:
                logger.info("📱 Sent notification to admin Telegram")
            else:
                logger.warning("Failed to send notification to admin Telegram")

            await telegram_client.close()
            return success

        except Exception as e:
            logger.error(f"Error sending Telegram notification: {e}")
            return False

    def _send_notification_sync(self, message: str, parse_mode: str = "HTML"):
        """Synchronous wrapper for sending Telegram notifications"""
        try:
            # Check if we're already in an event loop
            try:
                loop = asyncio.get_running_loop()
                # We're in an event loop, schedule the coroutine to run later
                import threading
                import concurrent.futures

                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        result = new_loop.run_until_complete(self._send_telegram_notification(message, parse_mode))
                        return result
                    finally:
                        new_loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result(timeout=30)  # 30 second timeout

            except RuntimeError:
                # No event loop running, we can create our own
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(self._send_telegram_notification(message, parse_mode))
                    return result
                finally:
                    loop.close()

        except Exception as e:
            logger.error(f"Error in sync notification wrapper: {e}")
            return False

    def send_schedule_summary(self):
        """Send upcoming Instagram posts schedule to admin"""
        try:
            now_tehran = datetime.now(self.tehran_tz)

            # Get scheduled posts for next 24 hours
            upcoming_posts = []
            for post in self.scheduled_posts:
                if post['status'] != 'scheduled':
                    continue

                try:
                    scheduled_time_naive = datetime.fromisoformat(post['scheduled_time'])
                    scheduled_time_utc = pytz.UTC.localize(scheduled_time_naive)
                    scheduled_time_tehran = scheduled_time_utc.astimezone(self.tehran_tz)

                    # Check if within next 24 hours
                    time_diff = scheduled_time_tehran - now_tehran
                    if timedelta(0) <= time_diff <= timedelta(hours=24):
                        upcoming_posts.append({
                            'id': post['id'],
                            'time_tehran': scheduled_time_tehran,
                            'caption': post['caption'][:100] + '...' if len(post['caption']) > 100 else post['caption']
                        })
                except Exception:
                    continue

            # Sort by time
            upcoming_posts.sort(key=lambda x: x['time_tehran'])

            # Create message
            if upcoming_posts:
                message = "📅 <b>Upcoming Instagram Posts (Next 24h)</b>\n\n"
                for i, post in enumerate(upcoming_posts, 1):
                    time_str = post['time_tehran'].strftime('%H:%M')
                    message += f"{i}. <b>{time_str}</b> - {post['caption']}\n\n"
                message += f"🕐 Current time: {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                message += f"📊 Total scheduled: {len(upcoming_posts)} posts"
            else:
                message = "📅 <b>Instagram Schedule</b>\n\n"
                message += "No posts scheduled for the next 24 hours.\n\n"
                message += f"🕐 Current time: {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)"

            # Send notification
            self._send_notification_sync(message)
            logger.info(f"📅 Sent schedule summary with {len(upcoming_posts)} upcoming posts")

        except Exception as e:
            logger.error(f"Error sending schedule summary: {e}")

    def _check_daily_summary(self):
        """Check if it's time to send daily summary (9 AM Tehran time)"""
        try:
            now_tehran = datetime.now(self.tehran_tz)

            # Check if it's 9 AM Tehran time (within 1 minute window)
            if now_tehran.hour == 9 and now_tehran.minute == 0:
                # Check if we already sent summary today
                today_str = now_tehran.strftime('%Y-%m-%d')
                last_summary_file = Config.BASE_DIR / "last_summary_date.txt"

                try:
                    if last_summary_file.exists():
                        with open(last_summary_file, 'r') as f:
                            last_summary_date = f.read().strip()
                        if last_summary_date == today_str:
                            return  # Already sent today
                except Exception:
                    pass

                # Send summary and record the date
                logger.info("Sending daily schedule summary (9 AM Tehran time)")
                self.send_schedule_summary()

                try:
                    with open(last_summary_file, 'w') as f:
                        f.write(today_str)
                except Exception as e:
                    logger.warning(f"Could not save last summary date: {e}")

        except Exception as e:
            logger.error(f"Error checking daily summary: {e}")

    def check_and_execute_posts(self):
        """Check for posts that should be executed now"""
        try:
            now_utc = datetime.utcnow()
            now_tehran = pytz.UTC.localize(now_utc).astimezone(self.tehran_tz)
            executed_count = 0

            # Only execute posts during posting hours (9 AM to 12 midnight Tehran time)
            if not (self.posting_start_hour <= now_tehran.hour < self.posting_end_hour):
                logger.info(f"Outside posting hours: current hour {now_tehran.hour}, posting window {self.posting_start_hour}-{self.posting_end_hour}")
                return

            logger.info(f"Checking {len(self.scheduled_posts)} scheduled posts at {now_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')}")

            for schedule_entry in self.scheduled_posts:
                if schedule_entry['status'] != 'scheduled':
                    continue

                # Parse scheduled time as UTC (since it was stored as UTC without timezone info)
                scheduled_time_naive = datetime.fromisoformat(schedule_entry['scheduled_time'])
                scheduled_time_utc = pytz.UTC.localize(scheduled_time_naive)

                logger.info(f"Post {schedule_entry['id']}: scheduled for {scheduled_time_utc.astimezone(self.tehran_tz).strftime('%Y-%m-%d %H:%M:%S %Z')}")

                if pytz.UTC.localize(now_utc) >= scheduled_time_utc:
                    logger.info(f"Executing scheduled post: {schedule_entry['id']}")
                    if self._execute_scheduled_post(schedule_entry):
                        executed_count += 1

            if executed_count > 0:
                self._save_schedule()
                logger.info(f"Executed {executed_count} scheduled posts")

        except Exception as e:
            logger.error(f"Error checking and executing posts: {e}")
    
    def start_scheduler(self):
        """Start the scheduler in a separate thread"""
        # Don't start scheduler in instant posting mode
        if Config.INSTANT_POSTING_MODE:
            logger.info("Scheduler disabled - running in instant posting mode")
            return

        if self.is_running:
            logger.warning("Scheduler is already running")
            return

        self.is_running = True

        # Schedule the check function to run every minute
        schedule.every(1).minutes.do(self.check_and_execute_posts)

        # Schedule daily summary - we'll handle Tehran timezone manually in the check function
        schedule.every(1).minutes.do(self._check_daily_summary)

        def run_scheduler():
            logger.info("Post scheduler started")
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            logger.info("Post scheduler stopped")

        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("Post scheduler stopped")
    
    def get_schedule_status(self) -> Dict[str, Any]:
        """Get current schedule status"""
        try:
            total_scheduled = len([p for p in self.scheduled_posts if p['status'] == 'scheduled'])
            total_posted = len([p for p in self.scheduled_posts if p['status'] == 'posted'])
            total_failed = len([p for p in self.scheduled_posts if p['status'] == 'failed'])
            
            # Get next scheduled post
            next_post = None
            next_post_time = None
            
            scheduled_posts = [p for p in self.scheduled_posts if p['status'] == 'scheduled']
            if scheduled_posts:
                scheduled_posts.sort(key=lambda x: x['scheduled_time'])
                next_post = scheduled_posts[0]
                next_post_time = datetime.fromisoformat(next_post['scheduled_time'])
            
            return {
                'is_running': self.is_running,
                'total_scheduled': total_scheduled,
                'total_posted': total_posted,
                'total_failed': total_failed,
                'next_post_time': next_post_time.isoformat() if next_post_time else None,
                'next_post_id': next_post['id'] if next_post else None
            }
            
        except Exception as e:
            logger.error(f"Error getting schedule status: {e}")
            return {}
    
    def cleanup_old_entries(self, days_old: int = 30):
        """Clean up old schedule entries"""
        try:
            # Use Tehran timezone for cutoff date to match created_at timestamps
            cutoff_date = datetime.now(self.tehran_tz) - timedelta(days=days_old)

            original_count = len(self.scheduled_posts)

            filtered_posts = []
            for entry in self.scheduled_posts:
                try:
                    # Parse created_at timestamp (may be timezone-aware or naive)
                    created_at_str = entry.get('created_at', '')
                    if created_at_str:
                        created_at = datetime.fromisoformat(created_at_str)

                        # If timezone-naive, assume it's Tehran time
                        if created_at.tzinfo is None:
                            created_at = self.tehran_tz.localize(created_at)

                        # Keep if newer than cutoff or still scheduled
                        if created_at > cutoff_date or entry['status'] == 'scheduled':
                            filtered_posts.append(entry)
                    else:
                        # Keep entries without created_at timestamp
                        filtered_posts.append(entry)

                except Exception as e:
                    logger.warning(f"Error parsing created_at for entry {entry.get('id', 'unknown')}: {e}")
                    # Keep entries with parsing errors to be safe
                    filtered_posts.append(entry)

            self.scheduled_posts = filtered_posts
            removed_count = original_count - len(self.scheduled_posts)

            if removed_count > 0:
                self._save_schedule()
                logger.info(f"Cleaned up {removed_count} old schedule entries")

            return removed_count

        except Exception as e:
            logger.error(f"Error cleaning up old entries: {e}")
            return 0

    def ensure_hourly_posting(self) -> int:
        """
        Ensure there's at least one post scheduled for each active hour.
        Simple logic: if no posts scheduled for current hour, schedule one from available videos.

        Returns:
            Number of posts scheduled
        """
        try:
            # Check if we're in posting hours
            current_time = datetime.now(self.tehran_tz)
            current_hour = current_time.hour

            posting_start = Config.POSTING_HOURS_START
            posting_end = Config.POSTING_HOURS_END

            if not (posting_start <= current_hour < posting_end):
                logger.info(f"Outside posting hours: current hour {current_hour}, posting window {posting_start}-{posting_end}")
                return 0

            # Check if we already have a post scheduled for this hour
            hour_start = current_time.replace(minute=0, second=0, microsecond=0)
            hour_end = hour_start + timedelta(hours=1)

            # Ensure timezone awareness for comparisons
            if hour_start.tzinfo is None:
                hour_start = self.tehran_tz.localize(hour_start)
            if hour_end.tzinfo is None:
                hour_end = self.tehran_tz.localize(hour_end)

            posts_this_hour = []
            for post in self.scheduled_posts:
                try:
                    post_time = datetime.fromisoformat(post['scheduled_time'].replace('+0330', '+03:30'))
                    if post_time.tzinfo is None:
                        post_time = self.tehran_tz.localize(post_time)
                    if hour_start <= post_time < hour_end and post['status'] in ['pending', 'executing']:
                        posts_this_hour.append(post)
                except Exception as e:
                    logger.debug(f"Error parsing post time: {e}")
                    continue

            if posts_this_hour:
                logger.info(f"✅ Already have {len(posts_this_hour)} posts scheduled for hour {current_hour}")
                return 0

            # Get available videos that are ready for posting
            ready_messages = self.message_processor.get_ready_for_posting()

            if not ready_messages:
                logger.info(f"⚠️  No videos available for posting in hour {current_hour}")
                return 0

            # Schedule one post for this hour (random selection from available videos)
            import random
            selected_message = random.choice(ready_messages)

            # Schedule for a random time within the next few minutes (shorter for testing)
            if Config.TEST_MODE:
                # Test mode: schedule within next 2-5 minutes for faster testing
                schedule_time = current_time + timedelta(minutes=random.randint(2, 5))
                logger.info(f"🧪 TEST MODE: Scheduling post for {schedule_time.strftime('%H:%M:%S')}")
            else:
                # Real mode: schedule within next 30 minutes
                schedule_time = current_time + timedelta(minutes=random.randint(5, 30))

            success = self.schedule_post(selected_message)

            if success:
                logger.info(f"🎯 Scheduled post for hour {current_hour}: message {selected_message.telegram_message.message_id}")
                return 1
            else:
                logger.warning(f"❌ Failed to schedule post for hour {current_hour}")
                return 0

        except Exception as e:
            logger.error(f"Error in ensure_hourly_posting: {e}")
            return 0
