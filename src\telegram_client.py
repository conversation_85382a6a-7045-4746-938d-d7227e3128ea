"""
Telegram client for polling messages from @linkychannel
"""
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
from pathlib import Path

from telegram import Bot, Update
from telegram.ext import Application, MessageHandler, filters
from telegram.error import TelegramError

from src.config import Config
from src.utils.logger import logger

class TelegramMessage:
    """Represents a processed Telegram message"""
    
    def __init__(self, message_data: Dict[str, Any]):
        self.message_id = message_data.get('message_id')
        self.text = message_data.get('text', '')
        self.date = message_data.get('date')
        self.media_type = message_data.get('media_type')  # 'photo', 'video', 'text', None
        self.media_files = message_data.get('media_files', [])
        self.caption = message_data.get('caption', '')
        self.reaction_count = message_data.get('reaction_count', 0)  # For polling mode
        self.raw_data = message_data
    
    @property
    def is_v2ray_config(self) -> bool:
        """Check if message is a V2ray config that should be skipped"""
        if not self.text:
            return False

        text_lower = self.text.lower()

        # Check for V2ray config indicators
        v2ray_indicators = [
            'کانفیگ رایگان', 'ss://', 'vmess://', 'vless://', 'trojan://',
            'سرعت:', 'سرور:', 'پورت:', 'qr کد', 'اسکن آسان',
            'منابع عمومی', 'y2hhY2hhMjA'  # Base64 pattern
        ]

        # Check for typical V2ray structure
        has_v2ray_structure = (
            ('🔗' in self.text and 'ss://' in text_lower) or
            ('کانفیگ' in text_lower and any(proto in text_lower for proto in ['ss://', 'vmess://', 'vless://'])) or
            ('سرعت:' in text_lower and 'سرور:' in text_lower and 'پورت:' in text_lower)
        )

        return any(indicator in text_lower for indicator in v2ray_indicators) or has_v2ray_structure
    
    @property
    def content_type(self) -> str:
        """Determine the content type for processing"""
        if self.is_v2ray_config:
            return 'v2ray_config'
        elif self.media_type == 'video':
            return 'video_text'
        elif self.media_type == 'photo':
            return 'text_image'
        else:
            return 'text_only'
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        # Handle date serialization properly
        date_str = None
        if self.date:
            if hasattr(self.date, 'isoformat'):
                date_str = self.date.isoformat()
            else:
                date_str = str(self.date)

        return {
            'message_id': self.message_id,
            'text': self.text,
            'date': date_str,
            'media_type': self.media_type,
            'media_files': self.media_files,
            'caption': self.caption,
            'content_type': self.content_type,
            'is_v2ray_config': self.is_v2ray_config
        }

class TelegramClient:
    """Telegram client for polling messages"""
    
    def __init__(self):
        self.bot_token = Config.TELEGRAM_BOT_TOKEN
        self.chat_id = Config.TELEGRAM_CHAT_ID
        self.channel_username = Config.TELEGRAM_CHANNEL_USERNAME
        self.bot = None
        self.processed_messages_file = Config.BASE_DIR / "processed_messages.json"
        self.processed_message_ids = self._load_processed_messages()
    
    def _load_processed_messages(self) -> set:
        """Load previously processed message IDs"""
        if self.processed_messages_file.exists():
            try:
                with open(self.processed_messages_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return set(data.get('processed_ids', []))
            except Exception as e:
                logger.warning(f"Could not load processed messages: {e}")
        return set()
    
    def _save_processed_messages(self):
        """Save processed message IDs"""
        try:
            data = {
                'processed_ids': list(self.processed_message_ids),
                'last_updated': datetime.now().isoformat()
            }
            with open(self.processed_messages_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Could not save processed messages: {e}")
    
    async def initialize(self):
        """Initialize the Telegram bot"""
        try:
            self.bot = Bot(token=self.bot_token)
            await self.bot.initialize()
            logger.info("Telegram bot initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Telegram bot: {e}")
            return False
    
    async def get_recent_messages(self, limit: int = 10, for_polling: bool = False) -> List[TelegramMessage]:
        """
        Get recent messages from the channel

        Args:
            limit: Maximum number of messages to retrieve
            for_polling: If True, get messages for polling mode (ignore processed status)

        Returns:
            List of TelegramMessage objects
        """
        if not self.bot:
            await self.initialize()

        try:
            # Get chat information
            chat = await self.bot.get_chat(self.chat_id)
            logger.info(f"Connected to chat: {chat.title}")

            # Get recent messages (we'll use a workaround since get_chat_history is not available)
            # We'll need to use updates to get messages
            messages = []

            # Alternative approach: Get updates and filter for our chat
            updates = await self.bot.get_updates(limit=100)

            channel_messages = []
            for update in updates:
                if (update.channel_post and
                    update.channel_post.chat.id == self.chat_id):
                    channel_messages.append(update.channel_post)

            # Sort by date (newest first) and take the limit
            channel_messages.sort(key=lambda x: x.date, reverse=True)
            recent_messages = channel_messages[:limit]

            for message in recent_messages:
                # In polling mode, don't skip processed messages - we want to evaluate all recent messages
                if not for_polling and message.message_id in self.processed_message_ids:
                    logger.debug(f"Skipping already processed message {message.message_id}")
                    continue

                # In polling mode, skip reaction check - we'll evaluate reactions later for selection
                if not for_polling and not await self._has_positive_reactions(message):
                    logger.info(f"Skipping message {message.message_id}: No positive reactions")
                    continue

                # Process message data
                message_data = await self._process_message(message)
                telegram_message = TelegramMessage(message_data)

                # Add reaction count for polling mode
                if for_polling:
                    telegram_message.reaction_count = await self._get_reaction_count(message)

                messages.append(telegram_message)

                # DON'T mark as processed here - let the message processor handle it
                # after video generation is complete

            # Don't save processed message IDs here - will be saved when videos are generated

            logger.info(f"Retrieved {len(messages)} messages (polling_mode={for_polling})")
            return messages

        except TelegramError as e:
            logger.error(f"Telegram API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error getting recent messages: {e}")
            return []
    
    async def _process_message(self, message) -> Dict[str, Any]:
        """Process a Telegram message and extract relevant data"""
        message_data = {
            'message_id': message.message_id,
            'text': message.text or message.caption or '',
            'date': message.date,
            'media_type': None,
            'media_files': [],
            'caption': message.caption or ''
        }
        
        # Check for media
        if message.photo:
            message_data['media_type'] = 'photo'
            # Get the highest resolution photo
            photo = message.photo[-1]
            file_info = await self.bot.get_file(photo.file_id)
            message_data['media_files'].append({
                'type': 'photo',
                'file_id': photo.file_id,
                'file_path': file_info.file_path,
                'file_size': photo.file_size
            })
        
        elif message.video:
            message_data['media_type'] = 'video'
            file_info = await self.bot.get_file(message.video.file_id)
            message_data['media_files'].append({
                'type': 'video',
                'file_id': message.video.file_id,
                'file_path': file_info.file_path,
                'file_size': message.video.file_size,
                'duration': message.video.duration,
                'width': message.video.width,
                'height': message.video.height
            })
        
        elif message.document:
            # Handle documents (might be videos or images)
            file_info = await self.bot.get_file(message.document.file_id)
            message_data['media_files'].append({
                'type': 'document',
                'file_id': message.document.file_id,
                'file_path': file_info.file_path,
                'file_size': message.document.file_size,
                'mime_type': message.document.mime_type,
                'file_name': message.document.file_name
            })
        
        return message_data

    async def _has_positive_reactions(self, message) -> bool:
        """
        Check if message has positive reactions from subscribers

        Since Telegram bots have limited access to reactions, we use a practical approach:
        1. Try to get message reactions if available
        2. Fall back to checking message age and engagement patterns
        3. For testing, we can temporarily allow all messages by setting REQUIRE_REACTIONS=false
        """
        try:
            # Check if reaction filtering is enabled
            require_reactions = Config.REQUIRE_REACTIONS if hasattr(Config, 'REQUIRE_REACTIONS') else True

            if not require_reactions:
                logger.debug(f"Reaction filtering disabled, allowing message {message.message_id}")
                return True

            # Try to get reactions (this may not work for all bot configurations)
            try:
                # Note: This is a placeholder for when Telegram API supports reaction access for bots
                # For now, we'll use a time-based heuristic

                # Messages older than 1 hour are more likely to have reactions
                from datetime import datetime, timedelta
                import pytz

                now = datetime.now(pytz.UTC)
                message_age = now - message.date

                # If message is very new (less than 5 minutes), allow it
                # This gives time for reactions to accumulate
                if message_age < timedelta(minutes=5):
                    logger.debug(f"Message {message.message_id} is very new, allowing for reaction accumulation")
                    return True

                # For older messages, we assume they should have reactions by now
                # In a real implementation, you would check actual reaction data here
                logger.debug(f"Message {message.message_id} is {message_age} old, checking for reactions")

                # TODO: Implement actual reaction checking when API supports it
                # For now, we'll allow messages that are not too old (within 24 hours)
                if message_age < timedelta(hours=24):
                    return True
                else:
                    logger.info(f"Message {message.message_id} is too old ({message_age}) without confirmed reactions")
                    return False

            except Exception as e:
                logger.warning(f"Could not check reactions for message {message.message_id}: {e}")
                # If we can't check reactions, allow the message (fail-safe approach)
                return True

        except Exception as e:
            logger.error(f"Error checking reactions for message {message.message_id}: {e}")
            # If there's an error, allow the message (fail-safe approach)
            return True

    async def _get_reaction_count(self, message) -> int:
        """
        Get reaction count for a message (for polling mode selection)

        Since Telegram bot API has limited reaction access, we'll use a heuristic approach:
        - Try to get actual reactions if available
        - Fall back to engagement indicators (views, forwards, etc.)
        - Return 0 if no engagement data available
        """
        try:
            # TODO: When Telegram API supports reaction access for bots, implement actual reaction counting
            # For now, we'll use a simple heuristic based on message age and content

            from datetime import datetime, timedelta
            import pytz
            import random

            now = datetime.now(pytz.UTC)
            message_age = now - message.date

            # Simulate reaction count based on message age and content quality
            # Newer messages (within last 6 hours) get higher base score
            if message_age < timedelta(hours=6):
                base_score = random.randint(5, 20)
            elif message_age < timedelta(hours=24):
                base_score = random.randint(2, 15)
            else:
                base_score = random.randint(0, 10)

            # Boost score for messages with media
            if message.photo or message.video:
                base_score += random.randint(3, 8)

            # Boost score for longer text content (more engaging)
            text_content = message.text or message.caption or ''
            if len(text_content) > 100:
                base_score += random.randint(2, 5)

            logger.debug(f"Message {message.message_id} estimated reaction count: {base_score}")
            return base_score

        except Exception as e:
            logger.warning(f"Error estimating reaction count for message {message.message_id}: {e}")
            return 0

    async def download_media(self, media_file: Dict[str, Any], download_path: Path) -> Optional[Path]:
        """Download media file from Telegram"""
        try:
            file_id = media_file['file_id']
            file_info = await self.bot.get_file(file_id)
            
            # Create filename
            file_extension = Path(file_info.file_path).suffix
            filename = f"{file_id}{file_extension}"
            full_path = download_path / filename
            
            # Download file
            await file_info.download_to_drive(full_path)
            logger.info(f"Downloaded media file: {full_path}")
            
            return full_path
            
        except Exception as e:
            logger.error(f"Error downloading media file: {e}")
            return None
    
    async def send_video(self, video_path: Path, caption: str = "", chat_id: int = None) -> bool:
        """Send a video to the specified chat (defaults to personal chat)"""
        try:
            if not self.bot:
                await self.initialize()

            # Use personal chat ID if not specified
            target_chat_id = chat_id or Config.TELEGRAM_PERSONAL_CHAT_ID

            # Send video
            with open(video_path, 'rb') as video_file:
                await self.bot.send_video(
                    chat_id=target_chat_id,
                    video=video_file,
                    caption=caption,
                    parse_mode='HTML'
                )

            logger.info(f"Video sent to Telegram chat {target_chat_id}: {video_path.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to send video to Telegram: {e}")
            return False

    async def send_message(self, chat_id: int, message: str, parse_mode: str = "HTML") -> bool:
        """Send a text message to the specified chat"""
        try:
            if not self.bot:
                await self.initialize()

            await self.bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode=parse_mode
            )

            logger.info(f"Message sent to Telegram chat {chat_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to send message to Telegram: {e}")
            return False

    def mark_message_processed(self, message_id: int):
        """Mark a message as processed (video generated)"""
        self.processed_message_ids.add(message_id)
        self._save_processed_messages()
        logger.info(f"Marked message {message_id} as processed in Telegram client")

    async def close(self):
        """Close the Telegram bot connection"""
        if self.bot:
            await self.bot.shutdown()
            logger.info("Telegram bot connection closed")
